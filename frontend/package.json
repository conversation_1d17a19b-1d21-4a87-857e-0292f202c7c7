{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint --fix", "type-check": "tsc --noEmit", "type-check:strict": "tsc --noEmit -p tsconfig.strict.json", "type-check:auth": "tsc --noEmit -p tsconfig.auth.json", "type-check:cypress": "tsc --noEmit --skipLib<PERSON>heck cypress/**/*.ts", "type-check:all": "npm run type-check && npm run type-check:strict && npm run type-check:cypress", "generate:supabase-types": "supabase gen types typescript --project-id anwefmklplkjxkmzpnva > src/lib/supabase/database.types.ts", "prepare": "husky", "test": "vitest", "test:watch": "vitest --watch", "test:subscription": "vitest src/components/subscription", "test:subscription:service": "vitest src/lib/services/__tests__/subscription", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test:e2e:ci": "bash scripts/run-e2e-tests.sh", "test:e2e:ready": "bash scripts/run-ready-tests.sh", "test:e2e:auth": "bash scripts/run-ready-tests.sh --with-auth", "test:e2e:subscription": "bash scripts/run-subscription-e2e-tests.sh", "test:component": "bash scripts/run-component-tests.sh", "test:component:open": "cypress open --component", "test:auth-flow": "cypress run --browser chrome --spec cypress/e2e/auth-flow.cy.ts", "setup:test-data": "ts-node scripts/setup-test-data.ts", "setup:test-users": "ts-node scripts/setup-test-users.ts", "setup:auth-test": "ts-node scripts/setup-auth-test.ts", "validate-react-types": "node scripts/validate-react-types.js", "preinstall": "node scripts/validate-react-types.js || true"}, "dependencies": {"@copilotkit/backend": "0.37.0", "@copilotkit/react-core": "1.8.12", "@copilotkit/react-textarea": "1.8.12", "@copilotkit/react-ui": "1.8.12", "@copilotkit/runtime": "1.8.12", "@copilotkit/shared": "1.8.12", "@fingerprintjs/fingerprintjs": "^3.4.0", "@fingerprintjs/fingerprintjs-pro-react": "^2.6.3", "@google-cloud/storage": "^7.16.0", "@google/generative-ai": "^0.24.1", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@langchain/community": "^0.3.29", "@langchain/core": "^0.3.39", "@langchain/langgraph": "^0.2.45", "@langchain/openai": "^0.4.3", "@marsidev/react-turnstile": "^1.1.0", "@playwright/test": "^1.52.0", "@radix-ui/number": "^1.1.1", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.7", "@simplewebauthn/server": "^13.1.1", "@simplewebauthn/types": "^12.0.0", "@supabase/ssr": "^0.1.0", "@supabase/supabase-js": "^2.49.1", "@tanstack/react-table": "^8.21.3", "@types/pg": "^8.11.14", "@types/qrcode": "^1.5.5", "@types/qrcode.react": "^1.0.5", "@types/recharts": "^1.8.29", "@upstash/redis": "^1.34.5", "buffer": "^6.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^0.2.0", "crypto-browserify": "^3.12.1", "date-fns": "^3.6.0", "drizzle-kit": "^0.31.0", "drizzle-orm": "^0.43.1", "framer-motion": "^12.3.0", "ioredis": "^5.6.1", "lucide-react": "^0.474.0", "neo4j-driver": "^5.28.1", "next": "15.1.6", "node-cache": "^5.1.2", "onnxruntime-web": "^1.21.0", "openai": "^4.85.1", "pg": "^8.15.6", "qrcode": "^1.5.4", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-error-boundary": "^4.1.2", "react-hook-form": "^7.54.2", "recharts": "^2.15.3", "sonner": "^2.0.1", "stream-browserify": "^3.0.0", "swr": "^2.3.3", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/cypress": "^0.1.6", "@types/ioredis": "^4.28.10", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.11.30", "@types/react": "18.3.1", "@types/react-dom": "18.3.1", "@types/testing-library__jest-dom": "^5.14.9", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.18", "cypress": "^13.6.6", "daisyui": "^4.9.0", "eslint": "9.2.0", "eslint-config-next": "15.1.6", "eslint-config-prettier": "^10.1.2", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^15.5.1", "node-mocks-http": "^1.16.2", "postcss": "^8.4.37", "prettier": "^3.5.3", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.4.3", "vitest": "^3.1.4", "whatwg-fetch": "^3.6.20"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "package.json": ["npm run validate-react-types"], "src/lib/supabase/database.types.ts": "npm run type-check:strict", "src/lib/types/**/*.ts": "npm run type-check:strict", "src/types/**/*.ts": "npm run type-check:strict"}}