#!/usr/bin/env node

/**
 * React Types Version Validator
 * 
 * This script ensures that @types/react and @types/react-dom remain at version 18.3.1
 * to prevent TypeScript compilation errors due to version mismatches with React 18.3.1
 */

const fs = require('fs');
const path = require('path');

const REQUIRED_REACT_TYPES_VERSION = '18.3.1';
const REQUIRED_REACT_DOM_TYPES_VERSION = '18.3.1';

function validateReactTypes() {
  console.log('🔍 Validating React types versions...');
  
  try {
    // Read package.json
    const packageJsonPath = path.join(__dirname, '..', 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    const reactTypesVersion = packageJson.devDependencies['@types/react'];
    const reactDomTypesVersion = packageJson.devDependencies['@types/react-dom'];
    
    let hasErrors = false;
    
    // Check @types/react version
    if (reactTypesVersion !== REQUIRED_REACT_TYPES_VERSION) {
      console.error(`❌ ERROR: @types/react version mismatch!`);
      console.error(`   Expected: ${REQUIRED_REACT_TYPES_VERSION}`);
      console.error(`   Found: ${reactTypesVersion}`);
      console.error(`   This will cause TypeScript compilation errors.`);
      hasErrors = true;
    } else {
      console.log(`✅ @types/react version is correct: ${reactTypesVersion}`);
    }
    
    // Check @types/react-dom version
    if (reactDomTypesVersion !== REQUIRED_REACT_DOM_TYPES_VERSION) {
      console.error(`❌ ERROR: @types/react-dom version mismatch!`);
      console.error(`   Expected: ${REQUIRED_REACT_DOM_TYPES_VERSION}`);
      console.error(`   Found: ${reactDomTypesVersion}`);
      console.error(`   This will cause TypeScript compilation errors.`);
      hasErrors = true;
    } else {
      console.log(`✅ @types/react-dom version is correct: ${reactDomTypesVersion}`);
    }
    
    if (hasErrors) {
      console.error(`\n🚨 CRITICAL: React types version mismatch detected!`);
      console.error(`\n📋 To fix this issue, run:`);
      console.error(`   npm install @types/react@${REQUIRED_REACT_TYPES_VERSION} @types/react-dom@${REQUIRED_REACT_DOM_TYPES_VERSION} --save-dev --save-exact`);
      console.error(`\n💡 This ensures compatibility with React 18.3.1 runtime.`);
      
      // Exit with error code to fail CI/CD if needed
      process.exit(1);
    }
    
    console.log(`\n🎉 All React types versions are correctly aligned!`);
    
    // Additional check: Verify no caret (^) or tilde (~) prefixes
    if (reactTypesVersion.includes('^') || reactTypesVersion.includes('~')) {
      console.warn(`⚠️  WARNING: @types/react has version range prefix (${reactTypesVersion})`);
      console.warn(`   Consider using exact version to prevent automatic updates.`);
    }
    
    if (reactDomTypesVersion.includes('^') || reactDomTypesVersion.includes('~')) {
      console.warn(`⚠️  WARNING: @types/react-dom has version range prefix (${reactDomTypesVersion})`);
      console.warn(`   Consider using exact version to prevent automatic updates.`);
    }
    
  } catch (error) {
    console.error(`❌ Failed to validate React types:`, error.message);
    process.exit(1);
  }
}

// Check if running in CI environment
const isCI = process.env.CI === 'true' || process.env.GITHUB_ACTIONS === 'true';

if (isCI) {
  console.log('🤖 Running in CI environment - enforcing strict validation');
}

validateReactTypes();
