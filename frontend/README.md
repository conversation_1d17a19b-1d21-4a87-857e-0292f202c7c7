This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

### Prerequisites

Before running the development server, make sure to install dependencies:

```bash
npm install
```

This will install all required dependencies including:
- React and Next.js
- TypeScript types (@types/react, @types/node)
- UI components (shadcn/ui, Radix UI)
- Icons (Lucide React)
- Other project dependencies

### Development Server

After installing dependencies, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [<PERSON>eist](https://vercel.com/font), a new font family for Vercel.

### Troubleshooting

#### TypeScript Errors: "Cannot find module 'react'" or UI Component Issues

If you encounter TypeScript errors like:
- `Cannot find module 'react' or its corresponding type declarations`
- `Cannot find module 'lucide-react'`
- Issues with Button, Card, Tabs, Dialog components
- Missing shadcn/ui component types

**Solution**: Install dependencies first:
```bash
npm install
```

This is the most common cause of TypeScript compilation errors in this project. The `node_modules` directory contains all the necessary type definitions and dependencies.

#### Type Checking

To verify TypeScript compilation:
```bash
npm run type-check
```

To run strict type checking:
```bash
npm run type-check:strict
```

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Authentication Redirect Race Condition: Issue & Solution

### Problem
A race condition was discovered where authenticated users were sometimes redirected to `/login` immediately after a successful login. This happened because the AuthenticatedLayout component checked for a valid session before the SessionProvider had finished asynchronously loading the session from Supabase. As a result, the session was initially `undefined`, triggering a false redirect.

### Solution
We added an `isLoading` state to the SessionProvider. The AuthenticatedLayout now waits for `isLoading` to be `false` before checking the session value. Only after the session is confirmed as loaded does it run the redirect logic. This prevents false redirects and ensures a seamless authentication experience for users.

**Key Points:**
- Always wait for the session loading state before performing authentication checks on the client.
- This approach synchronizes client and server authentication, preventing UI flicker and unwanted redirects.

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

# Project Architecture

## Overview
This architecture follows a service-oriented approach, dividing the application into distinct layers that handle different concerns. This promotes separation of concerns, maintainability, and scalability.

### 1. API Routes
API routes serve as the entry points for client requests, handling HTTP methods (GET, POST, PUT, DELETE) and processing requests to generate responses.

- **Structure**: Each entity (clients, cases, tasks, etc.) has its own set of API routes organized under `/api/{entity}`.
- **Role**:
  - Parse incoming requests and extract parameters.
  - Validate request data against defined schemas.
  - Call corresponding service methods to perform operations.
  - Format and send responses back to the client.

### 2. Middleware
Middleware functions enhance API routes with additional functionality, such as authentication and authorization.

- **withAuth()**: A middleware that checks if the user is authenticated. It verifies the JWT token and extracts user claims (like `role` and `tenant_id`). If the user is not authenticated, it returns an error response.
- **Role-Based Access Control**: The middleware enforces access control by allowing only users with specific roles (e.g., partner, attorney) to access certain routes.

### 3. Auth Helpers
The `auth-helpers` file contains functions that facilitate authentication and authorization processes.

- **Functions**:
  - `createServerClientForUser()`: Creates a Supabase client for user-authenticated routes using the Anon Key.
  - `createServiceClient()`: Creates a Supabase client for admin operations using the Service Key.
- **Role**:
  - Simplifies the creation of Supabase clients with appropriate permissions based on the user's authentication status.
  - Ensures that API routes can easily access the correct client for performing database operations.

### 4. Services
Services encapsulate the business logic and data access layer. They handle interactions with the database.

- **Structure**: Each entity has a dedicated service (e.g., `ClientService`, `CaseService`, etc.) defining methods for CRUD operations.
- **Role**:
  - Perform data validation and transformation.
  - Interact with the database using Supabase client methods.
  - Handle relationships between entities (e.g., clients and cases).
  - Return structured data to the API routes.

### 5. Database Interaction
Database interactions are performed through the Supabase client, which communicates with the PostgreSQL database.

- **Schema Separation**: The application uses a multi-tenant architecture, where tenant-specific data is stored in the `tenants` schema, ensuring data isolation and security.
- **Tenant ID Filtering**: All database queries include tenant ID filtering to ensure users can only access their own data.

### 6. Zod for Validation
Zod is used for schema validation of incoming request data, ensuring that only valid data is processed.

- **Role**:
  - Validate request bodies against defined schemas before processing.
  - Provide detailed error messages when validation fails.

### 7. JWT Claims
JWT tokens are used for authentication and authorization.

- **Claims Structure**: Tokens include claims such as `sub` (user ID), `email`, `role`, and `tenant_id`.
- **Role and Tenant Isolation**: The application enforces role-based access control and tenant isolation using these claims.

### 8. Error Handling
Consistent error handling is implemented throughout the application.

- **Error Responses**: API routes return structured error responses.

## 6. Rate Limiting and Resource Controls

The application implements a comprehensive rate limiting and resource control system to manage document uploads effectively. This ensures that tenants can only upload documents within their specified quotas and limits.

### Key Features:
- **Tenant Quotas**: Each tenant has specific limits on daily uploads, monthly uploads, maximum document size, and concurrent processing.
- **Rate Limit Middleware**: Applied to the document upload endpoint to check tenant limits before processing uploads.
- **Real-time Tracking**: Utilizes Redis for real-time counters to enforce limits immediately.
- **Admin Interface**: Accessible at `/admin/tenant-quotas`, where administrators can view and modify tenant quotas and monitor resource usage.

### Admin Interface for Quota Management
The admin interface for managing tenant quotas is available at `/admin/tenant-quotas`. This page allows administrators to:
- View current tenant quotas and resource usage.
- Modify resource limits for each tenant based on their subscription plans.
- Monitor overall system resource consumption through analytics.

### Usage Example
To check if a tenant can upload a document, the `RateLimitService` is used, which checks the tenant's current usage against their quotas and returns whether the upload is allowed or not.

# Client Intake System

## Overview
The client intake system is a critical component of the application, allowing law firms to collect and process information about new clients and cases. The system is designed to support multiple practice areas including Personal Injury, Criminal Defense, and Family Law.

## Database Schema

### Core Tables

1. **clients**
   - Primary client information storage
   - Key fields: `id`, `tenant_id`, `first_name`, `last_name`, `date_of_birth`, `email`, `phone_primary`
   - Employment fields: `occupation`, `employer`, `work_status`
   - Address stored as JSONB: `{ street, city, state, zip }`
   - Status tracking: `status`, `intake_date`, `conflict_check_status`

2. **cases**
   - Case information storage
   - Key fields: `id`, `tenant_id`, `title`, `description`, `client_id`
   - Practice area fields: `practice_area`, `case_type`, `intake_priority`
   - Status tracking: `status`, `opened_date`, `closed_date`
   - Previous consultation: `previously_consulted`
   - Additional details in `metadata` JSONB

3. **case_clients**
   - Junction table linking cases to clients (many-to-many)
   - Fields: `tenant_id`, `case_id`, `client_id`

4. **parties**
   - Information about non-client parties involved in cases
   - Key fields: `id`, `tenant_id`, `first_name`, `last_name`, `type`
   - Contact information: `email`, `phone_primary`
   - Address stored as JSONB: `{ street, city, state, zip }`

5. **case_parties**
   - Junction table linking cases to parties
   - Fields: `tenant_id`, `case_id`, `party_id`, `role`

6. **conflict_checks**
   - Tracks conflict of interest checks
   - Key fields: `id`, `tenant_id`, `case_id`, `party_id`, `status`
   - Details: `conflict_type`, `description`, `resolved_at`

### Column Structure

We've implemented dedicated columns for frequently queried fields instead of storing them in JSONB columns:

- **Employment Information**: Added `occupation`, `employer`, and `work_status` columns to the `clients` table
- **Practice Area Information**: Added `practice_area` and `intake_priority` columns to the `cases` table

## Form Implementation

### Staff Intake Form

The staff intake form (`/components/intake/staff-intake-form.tsx`) is structured into sections:

1. **Client Information**
   - Personal details (name, DOB, SSN)
   - Contact information (email, phone)
   - Address
   - Employment information

2. **Case Information**
   - Practice area (Personal Injury, Criminal Defense, Family Law)
   - Case type (specific to practice area)
   - Case description
   - Intake priority
   - Previous attorney consultation

3. **Other Parties**
   - Dynamic section for adding multiple parties
   - Name and relationship fields for each party

4. **Internal Notes**
   - Additional information for staff

### Form Validation

Form validation is implemented using Zod schemas:

```typescript
const intakeFormSchema = z.object({
  // Client information
  firstName: z.string().min(1, "First name is required"),
  middleName: z.string().optional(),
  lastName: z.string().min(1, "Last name is required"),
  dateOfBirth: z.date({ required_error: "Date of birth is required" }),
  ssn: z.string().optional(),
  email: z.string().email().optional(),
  phone: z.string().min(10, "Phone number must be at least 10 digits"),
  address: z.string().min(1, "Address is required"),
  city: z.string().min(1, "City is required"),
  state: z.string().min(1, "State is required"),
  zipCode: z.string().min(5, "Zip code must be at least 5 digits"),
  occupation: z.string().optional(),
  employer: z.string().optional(),
  workStatus: z.string().optional(),

  // Case information
  practiceArea: z.enum(["personal_injury", "criminal_defense", "family_law"]),
  caseType: z.string().min(1, "Case type is required"),
  caseDescription: z.string().min(1, "Case description is required"),
  intakePriority: z.enum(["high", "medium", "low"]),
  previousAttorneyConsultation: z.boolean().default(false),
  previousAttorneyDetails: z.string().optional(),

  // Other parties
  otherParties: z.array(
    z.object({
      name: z.string().optional(),
      relationship: z.string().optional(),
    })
  ).default([{ name: '', relationship: '' }]),

  // Internal notes
  internalNotes: z.string().optional(),
});
```

## Transaction Handling

To ensure data integrity, we use a PostgreSQL stored procedure for transaction handling:

### Database Function

```sql
CREATE OR REPLACE FUNCTION tenants.create_client_intake(
  p_client_data JSONB,
  p_case_data JSONB,
  p_other_parties JSONB
) RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_tenant_id UUID;
  v_client_id UUID;
  v_case_id UUID;
  v_party_id UUID;
  v_party JSONB;
  v_result JSONB;
  v_user_id UUID;
BEGIN
  -- Get tenant_id and user_id from JWT claims
  v_tenant_id := (auth.jwt() ->> 'tenant_id')::UUID;
  v_user_id := auth.uid();

  -- Insert client
  INSERT INTO tenants.clients (
    tenant_id, first_name, middle_name, last_name,
    date_of_birth, ssn, client_type, email, phone_primary,
    address, occupation, employer, work_status,
    status, intake_date, conflict_check_status, created_by
  ) VALUES (
    v_tenant_id, p_client_data->>'first_name', p_client_data->>'middle_name',
    p_client_data->>'last_name', (p_client_data->>'date_of_birth')::DATE,
    p_client_data->>'ssn', p_client_data->>'client_type', p_client_data->>'email',
    p_client_data->>'phone_primary', p_client_data->'address',
    p_client_data->>'occupation', p_client_data->>'employer',
    p_client_data->>'work_status', p_client_data->>'status',
    COALESCE((p_client_data->>'intake_date')::DATE, CURRENT_DATE),
    p_client_data->>'conflict_check_status', v_user_id
  )
  RETURNING id INTO v_client_id;

  -- Insert case
  INSERT INTO tenants.cases (
    tenant_id, client_id, title, description,
    practice_area, case_type, intake_priority,
    status, previously_consulted, metadata, created_by
  ) VALUES (
    v_tenant_id, v_client_id, p_case_data->>'title',
    p_case_data->>'description', p_case_data->>'practice_area',
    p_case_data->>'case_type', p_case_data->>'intake_priority',
    p_case_data->>'status', (p_case_data->>'previously_consulted')::BOOLEAN,
    p_case_data->'metadata', v_user_id
  )
  RETURNING id INTO v_case_id;

  -- Insert case_clients junction
  INSERT INTO tenants.case_clients (
    tenant_id, case_id, client_id
  ) VALUES (
    v_tenant_id, v_case_id, v_client_id
  );

  -- Process other parties
  IF p_other_parties IS NOT NULL AND jsonb_array_length(p_other_parties) > 0 THEN
    FOR i IN 0..jsonb_array_length(p_other_parties) - 1 LOOP
      v_party := p_other_parties->i;

      -- Skip empty party entries
      IF v_party->>'first_name' IS NOT NULL OR v_party->>'last_name' IS NOT NULL THEN
        -- Insert party and case_parties records
        -- [Implementation details omitted for brevity]
      END IF;
    END LOOP;
  END IF;

  RETURN jsonb_build_object('success', true, 'client_id', v_client_id, 'case_id', v_case_id);
END;
$$;
```

### Form Submission Implementation

The form submission in the React component calls this stored procedure:

```typescript
async function onSubmit(data: z.infer<typeof intakeFormSchema>) {
  try {
    setIsSubmitting(true);

    // Call the stored procedure with form data
    const { data: result, error } = await supabase.rpc('create_client_intake', {
      p_client_data: {
        // Client personal information
        first_name: data.firstName,
        middle_name: data.middleName || null,
        last_name: data.lastName,
        date_of_birth: data.dateOfBirth,
        // [Other client fields...]

        // Employment information (dedicated columns)
        occupation: data.occupation || null,
        employer: data.employer || null,
        work_status: data.workStatus || null,
      },
      p_case_data: {
        // Case information
        title: `${data.firstName} ${data.lastName} - ${data.caseType}`,
        description: data.caseDescription,

        // Using dedicated columns
        practice_area: data.practiceArea,
        case_type: data.caseType,
        intake_priority: data.intakePriority,

        // [Other case fields...]
      },
      p_other_parties: data.otherParties.filter(party => party.name)
        .map(party => {
          // Process other parties
          // [Implementation details...]
        })
    });

    if (error) throw new Error(`Transaction failed: ${error.message}`);

    toast.success('Client intake submitted successfully!');
    form.reset();
  } catch (error) {
    console.error('Error submitting client intake:', error);
    toast.error(`Failed to submit client intake: ${error instanceof Error ? error.message : 'Please try again.'}`);
  } finally {
    setIsSubmitting(false);
  }
}
```

## Benefits of This Implementation

1. **Data Integrity**: Using a transaction ensures all related records are created together or not at all
2. **Security**: Tenant isolation is enforced at the database level
3. **Performance**: Dedicated columns for frequently queried fields improve query performance
4. **Maintainability**: Clear separation of concerns between UI, validation, and data persistence
5. **Flexibility**: Support for multiple practice areas with practice-specific case types and appropriate status codes.
- **Logging**: Errors are logged for debugging and monitoring purposes.

### How They Work Together
1. **Client Request**: A client makes an HTTP request to an API route.
2. **Middleware Execution**: The request passes through the `withAuth()` middleware, which checks for authentication.
3. **Data Validation**: The API route validates the incoming request data using Zod schemas.
4. **Service Call**: The API route calls the corresponding service method to perform the requested operation (e.g., fetching a client).
5. **Database Interaction**: The service interacts with the Supabase client to perform CRUD operations.
6. **Response Generation**: The service returns the result to the API route, which formats it and sends it back to the client.
7. **Error Handling**: If any errors occur at any point, they are caught and handled appropriately, ensuring a smooth user experience.

### Conclusion
This architecture provides a robust framework for building scalable applications. By separating concerns into distinct layers, we achieve better maintainability, reusability, and security. The use of middleware, services, and validation ensures that our application is both user-friendly and secure.
