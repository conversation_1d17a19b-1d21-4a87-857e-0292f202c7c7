// Test script for MCP server
const { exec } = require('child_process');

// Run a query against the Supabase MCP server
function runQuery(query) {
  return new Promise((resolve, reject) => {
    const cmd = `claude chat --mcp supabase "Tell me the results of this query: ${query}"`;

    exec(cmd, (error, stdout, stderr) => {
      if (error) {
        reject(`exec error: ${error}`);
        return;
      }

      resolve(stdout);
    });
  });
}

// Test query to list tables
async function main() {
  try {
    const result = await runQuery("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'");
    console.log('Query Result:');
    console.log(result);
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
