/**
 * Unit tests for McpClient core functionality
 */

import { McpClient, McpApiError } from '../../index';

// Mock node-fetch
jest.mock('node-fetch', () => jest.fn());

const mockFetch = require('node-fetch') as jest.MockedFunction<typeof fetch>;

// Helper function to create mock Response objects
const createMockResponse = (options: {
  ok: boolean;
  status?: number;
  statusText?: string;
  json?: any;
  jsonError?: Error;
}): Response => ({
  ok: options.ok,
  status: options.status || (options.ok ? 200 : 500),
  statusText: options.statusText || (options.ok ? 'OK' : 'Internal Server Error'),
  headers: {} as Headers,
  type: 'basic',
  url: '',
  redirected: false,
  body: null,
  bodyUsed: false,
  clone: jest.fn().mockReturnValue({}),
  arrayBuffer: jest.fn().mockResolvedValue(new ArrayBuffer(0)),
  blob: jest.fn().mockResolvedValue(new Blob([])),
  formData: jest.fn().mockResolvedValue(new FormData()),
  text: jest.fn().mockResolvedValue(''),
  json: options.jsonError
    ? jest.fn().mockRejectedValue(options.jsonError)
    : jest.fn().mockResolvedValue(options.json || {})
} as unknown as Response);

describe('McpClient', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
    // Use real timers for client tests to avoid timeout issues
    jest.useRealTimers();
  });

  afterEach(() => {
    jest.clearAllTimers();
    jest.useRealTimers();
  });

  describe('Initialization', () => {
    it('should initialize with required configuration', () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-api-key'
      });

      expect(client).toBeDefined();
      const state = client.getCircuitBreakerState();
      expect(state.state).toBe('closed');
      expect(state.consecutiveFailures).toBe(0);
    });

    it('should strip trailing slash from baseUrl', () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com/',
        apiKey: 'test-api-key'
      });

      expect(client).toBeDefined();
      // The trailing slash removal is tested indirectly through request URLs
    });

    it('should apply custom configuration', () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-api-key',
        timeout: 5000,
        maxRetries: 1,
        retryDelay: 500
      });

      expect(client).toBeDefined();
      // Configuration values are private, but behavior can be tested
    });

    it('should use default values for optional configuration', () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-api-key'
      });

      expect(client).toBeDefined();
      // Default values: timeout=30000, maxRetries=3, retryDelay=1000
    });
  });

  describe('calculateDeadlines', () => {
    let client: McpClient;

    beforeEach(() => {
      client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-api-key',
        timeout: 5000,
        maxRetries: 2,
        retryDelay: 100
      });
    });

    it('should make successful request with correct parameters', async () => {
      const mockResponse = {
        deadlines: [
          {
            id: '1',
            name: 'Test Deadline',
            description: 'Test description',
            dueDate: '2025-01-15T00:00:00Z',
            priority: 'high',
            category: 'filing',
            isStatutory: true
          }
        ],
        jurisdiction: 'TX',
        triggerCode: 'ACCIDENT',
        startDate: '2023-01-15T00:00:00Z',
        practiceArea: 'personal-injury'
      };

      mockFetch.mockResolvedValueOnce(createMockResponse({
        ok: true,
        json: mockResponse
      }));

      const result = await client.calculateDeadlines(
        'TX',
        'ACCIDENT',
        '2023-01-15T00:00:00Z',
        'personal-injury'
      );

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://rules.ailexlaw.com/api/v1/deadlines/calculate',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'x-api-key': 'test-api-key'
          }),
          body: JSON.stringify({
            jurisdiction: 'TX',
            triggerCode: 'ACCIDENT',
            startDate: '2023-01-15T00:00:00Z',
            practiceArea: 'personal-injury'
          })
        })
      );
    });

    it('should handle request without optional practiceArea', async () => {
      const mockResponse = {
        deadlines: [],
        jurisdiction: 'TX',
        triggerCode: 'ACCIDENT',
        startDate: '2023-01-15T00:00:00Z'
      };

      mockFetch.mockResolvedValueOnce(createMockResponse({
        ok: true,
        json: mockResponse
      }));

      const result = await client.calculateDeadlines(
        'TX',
        'ACCIDENT',
        '2023-01-15T00:00:00Z'
      );

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://rules.ailexlaw.com/api/v1/deadlines/calculate',
        expect.objectContaining({
          body: JSON.stringify({
            jurisdiction: 'TX',
            triggerCode: 'ACCIDENT',
            startDate: '2023-01-15T00:00:00Z',
            practiceArea: undefined
          })
        })
      );
    });

    it('should throw McpApiError on API error response', async () => {
      const errorResponse = {
        message: 'Invalid jurisdiction',
        code: 'INVALID_JURISDICTION',
        details: { field: 'jurisdiction' }
      };

      // Mock all fetch calls to return the same error (for retries)
      mockFetch.mockResolvedValue(createMockResponse({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        json: errorResponse
      }));

      try {
        await client.calculateDeadlines('INVALID', 'ACCIDENT', '2023-01-15T00:00:00Z');
        fail('Expected McpApiError to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(McpApiError);
        expect((error as McpApiError).status).toBe(400);
        expect((error as McpApiError).code).toBe('INVALID_JURISDICTION');
        // The details should be the entire error response body
        expect((error as McpApiError).details).toEqual(errorResponse);
        expect((error as McpApiError).message).toBe('Invalid jurisdiction');
      }
    });

    it('should handle malformed JSON response gracefully', async () => {
      // Mock all fetch calls to return the same error (for retries)
      mockFetch.mockResolvedValue(createMockResponse({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        jsonError: new Error('Invalid JSON')
      }));

      await expect(
        client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z')
      ).rejects.toThrow(McpApiError);
    }, 15000); // Increase timeout to 15 seconds

    it('should include signal for timeout handling', async () => {
      mockFetch.mockImplementation((url, options) => {
        expect(options?.signal).toBeDefined();
        expect(options?.signal).toBeInstanceOf(AbortSignal);
        return Promise.resolve(createMockResponse({
          ok: true,
          json: { deadlines: [] }
        }));
      });

      await client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');
    });
  });

  describe('healthCheck', () => {
    let client: McpClient;

    beforeEach(() => {
      client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-api-key'
      });
    });

    it('should make successful health check request', async () => {
      const mockResponse = {
        status: 'healthy',
        timestamp: '2023-01-15T12:00:00Z',
        version: '1.0.0',
        uptime: 3600
      };

      mockFetch.mockResolvedValueOnce(createMockResponse({
        ok: true,
        json: mockResponse
      }));

      const result = await client.healthCheck();

      expect(result).toEqual(mockResponse);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://rules.ailexlaw.com/health',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'x-api-key': 'test-api-key'
          })
        })
      );
    });

    it('should handle unhealthy service response', async () => {
      // Mock all fetch calls to return the same error (for retries)
      mockFetch.mockResolvedValue(createMockResponse({
        ok: false,
        status: 503,
        statusText: 'Service Unavailable',
        json: {
          status: 'unhealthy',
          timestamp: '2023-01-15T12:00:00Z'
        }
      }));

      await expect(client.healthCheck()).rejects.toThrow(McpApiError);
    }, 15000); // Increase timeout to 15 seconds
  });

  describe('getCircuitBreakerState', () => {
    it('should return initial circuit breaker state', () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-api-key'
      });

      const state = client.getCircuitBreakerState();

      expect(state).toEqual({
        state: 'closed',
        consecutiveFailures: 0,
        lastFailureTime: 0
      });
    });

    it('should return updated state after failures', async () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-api-key',
        maxRetries: 0 // No retries for faster testing
      });

      mockFetch.mockResolvedValue(createMockResponse({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: { message: 'Server error' }
      }));

      // Make one failed request
      try {
        await client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');
      } catch (error) {
        // Expected to fail
      }

      const state = client.getCircuitBreakerState();
      expect(state.state).toBe('closed'); // Still closed after 1 failure
      expect(state.consecutiveFailures).toBe(1);
      expect(state.lastFailureTime).toBeGreaterThan(0);
    }, 15000); // Increase timeout to 15 seconds
  });

  describe('Error Handling', () => {
    let client: McpClient;

    beforeEach(() => {
      client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-api-key',
        maxRetries: 1,
        retryDelay: 10
      });
    });

    it('should create proper McpApiError for HTTP errors', async () => {
      const errorResponse = {
        message: 'Custom error message',
        code: 'CUSTOM_ERROR',
        details: { field: 'jurisdiction' }
      };

      // Mock all fetch calls to return the same error (for retries)
      mockFetch.mockResolvedValue(createMockResponse({
        ok: false,
        status: 422,
        statusText: 'Unprocessable Entity',
        json: errorResponse
      }));

      try {
        await client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');
        fail('Expected McpApiError to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(McpApiError);
        expect((error as McpApiError).status).toBe(422);
        expect((error as McpApiError).code).toBe('CUSTOM_ERROR');
        // The details should be the entire error response body
        expect((error as McpApiError).details).toEqual(errorResponse);
        expect((error as McpApiError).message).toBe('Custom error message');
      }
    });

    it('should handle network errors', async () => {
      // Mock all fetch calls to reject (for retries)
      mockFetch.mockRejectedValue(new Error('Network error'));

      await expect(
        client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z')
      ).rejects.toThrow('Network error');
    }, 15000); // Increase timeout to 15 seconds

    it('should provide default error message for HTTP errors without body', async () => {
      // Mock all fetch calls to return the same error (for retries)
      mockFetch.mockResolvedValue(createMockResponse({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: null
      }));

      try {
        await client.calculateDeadlines('TX', 'ACCIDENT', '2023-01-15T00:00:00Z');
        fail('Expected McpApiError to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(McpApiError);
        expect((error as McpApiError).message).toBe('HTTP 500: Internal Server Error');
      }
    }, 15000); // Increase timeout to 15 seconds
  });
});
