/**
 * Simple tests for MCP Client core functionality
 * These tests focus on the client logic without complex HTTP mocking
 */

import { McpClient, McpApiError } from '../index';

// Import fetch mock
import fetchMock from 'jest-fetch-mock';

describe('MCP Client Simple Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    fetchMock.resetMocks();
  });

  describe('Client Initialization', () => {
    it('should create a client instance', () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-key'
      });

      expect(client).toBeInstanceOf(McpClient);
    });

    it('should initialize circuit breaker in closed state', () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-key'
      });

      const state = client.getCircuitBreakerState();
      expect(state.state).toBe('closed');
      expect(state.consecutiveFailures).toBe(0);
      expect(state.lastFailureTime).toBe(0);
    });

    it('should handle custom configuration', () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com/',
        apiKey: 'test-key',
        timeout: 5000,
        maxRetries: 1,
        retryDelay: 500
      });

      expect(client).toBeInstanceOf(McpClient);
      
      // Verify circuit breaker is still properly initialized
      const state = client.getCircuitBreakerState();
      expect(state.state).toBe('closed');
    });
  });

  describe('McpApiError', () => {
    it('should create error with all properties', () => {
      const error = new McpApiError(
        'Test error',
        400,
        'TEST_CODE',
        { field: 'test' }
      );

      expect(error).toBeInstanceOf(Error);
      expect(error).toBeInstanceOf(McpApiError);
      expect(error.message).toBe('Test error');
      expect(error.status).toBe(400);
      expect(error.code).toBe('TEST_CODE');
      expect(error.details).toEqual({ field: 'test' });
    });

    it('should create error with minimal properties', () => {
      const error = new McpApiError('Simple error', 500);

      expect(error.message).toBe('Simple error');
      expect(error.status).toBe(500);
      expect(error.code).toBeUndefined();
      expect(error.details).toBeUndefined();
    });

    it('should have proper error name', () => {
      const error = new McpApiError('Test', 400);
      expect(error.name).toBe('McpApiError');
    });
  });

  describe('Circuit Breaker State Management', () => {
    it('should return state object with correct structure', () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-key'
      });

      const state = client.getCircuitBreakerState();

      expect(state).toHaveProperty('state');
      expect(state).toHaveProperty('consecutiveFailures');
      expect(state).toHaveProperty('lastFailureTime');
      
      expect(typeof state.state).toBe('string');
      expect(typeof state.consecutiveFailures).toBe('number');
      expect(typeof state.lastFailureTime).toBe('number');
    });

    it('should have valid circuit breaker states', () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-key'
      });

      const state = client.getCircuitBreakerState();
      const validStates = ['closed', 'open', 'half-open'];
      
      expect(validStates).toContain(state.state);
    });

    it('should maintain state consistency across calls', () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-key'
      });

      const state1 = client.getCircuitBreakerState();
      const state2 = client.getCircuitBreakerState();

      expect(state1).toEqual(state2);
    });
  });

  describe('Configuration Validation', () => {
    it('should handle baseUrl with trailing slash', () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com/',
        apiKey: 'test-key'
      });

      expect(client).toBeInstanceOf(McpClient);
    });

    it('should handle baseUrl without trailing slash', () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-key'
      });

      expect(client).toBeInstanceOf(McpClient);
    });

    it('should accept all configuration options', () => {
      const config = {
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-key',
        timeout: 10000,
        maxRetries: 5,
        retryDelay: 2000
      };

      const client = new McpClient(config);
      expect(client).toBeInstanceOf(McpClient);
    });
  });

  describe('Type Safety', () => {
    it('should enforce required configuration properties', () => {
      // This test verifies TypeScript compilation
      // If this compiles, the types are working correctly
      
      const validConfig = {
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-key'
      };

      const client = new McpClient(validConfig);
      expect(client).toBeInstanceOf(McpClient);
    });

    it('should provide proper return types', () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-key'
      });

      const state = client.getCircuitBreakerState();
      
      // TypeScript should enforce these types
      expect(typeof state.state).toBe('string');
      expect(typeof state.consecutiveFailures).toBe('number');
      expect(typeof state.lastFailureTime).toBe('number');
    });
  });

  describe('Error Handling', () => {
    it('should create proper error hierarchy', () => {
      const error = new McpApiError('Test error', 400);

      expect(error instanceof Error).toBe(true);
      expect(error instanceof McpApiError).toBe(true);
    });

    it('should preserve error stack trace', () => {
      const error = new McpApiError('Test error', 400);

      expect(error.stack).toBeDefined();
      expect(typeof error.stack).toBe('string');
    });
  });

  describe('Basic HTTP Integration', () => {
    it.skip('@integration should handle successful health check', async () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-key'
      });

      fetchMock.mockResponseOnce(JSON.stringify({
        status: 'healthy',
        timestamp: '2023-01-15T12:00:00Z'
      }));

      const result = await client.healthCheck();

      expect(result.status).toBe('healthy');
      expect(fetchMock).toHaveBeenCalledWith(
        'https://rules.ailexlaw.com/health',
        expect.objectContaining({
          headers: expect.objectContaining({
            'x-api-key': 'test-key'
          })
        })
      );
    });

    it.skip('@integration should handle API errors gracefully', async () => {
      const client = new McpClient({
        baseUrl: 'https://rules.ailexlaw.com',
        apiKey: 'test-key'
      });

      fetchMock.mockRejectOnce(new Error('Network error'));

      await expect(client.healthCheck()).rejects.toThrow('Network error');
    });
  });
});
