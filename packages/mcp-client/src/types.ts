/**
 * MCP Rules Engine API Types
 */

export interface DeadlineRequest {
  jurisdiction: string;
  triggerCode: string;
  startDate: string; // ISO 8601 date string
  practiceArea?: string;
}

export interface DeadlineResponse {
  deadlines: Deadline[];
  jurisdiction: string;
  triggerCode: string;
  startDate: string;
  practiceArea?: string;
}

export interface Deadline {
  id: string;
  name: string;
  description: string;
  dueDate: string; // ISO 8601 date string
  priority: 'high' | 'medium' | 'low';
  category: string;
  isStatutory: boolean;
  source?: string;
  notes?: string;
}

export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  version?: string;
  uptime?: number;
}

export interface McpClientConfig {
  baseUrl: string;
  apiKey: string;
  timeout?: number;
  maxRetries?: number;
  retryDelay?: number;
}

export interface ApiError {
  message: string;
  status: number;
  code?: string;
  details?: any;
}

export class McpApiError extends Error {
  public readonly status: number;
  public readonly code?: string;
  public readonly details?: any;

  constructor(message: string, status: number, code?: string, details?: any) {
    super(message);
    this.name = 'McpApiError';
    this.status = status;
    this.code = code;
    this.details = details;
  }
}
