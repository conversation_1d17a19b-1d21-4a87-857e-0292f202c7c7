{"version": 3, "names": ["_importInjector", "require", "_isModule", "addDefault", "path", "importedSource", "opts", "ImportInjector", "addNamed", "name", "addNamespace", "addSideEffect"], "sources": ["../src/index.ts"], "sourcesContent": ["import ImportInjector, { type ImportOptions } from \"./import-injector.ts\";\nimport type { NodePath } from \"@babel/traverse\";\nimport type * as t from \"@babel/types\";\n\nexport { ImportInjector };\n\nexport { default as isModule } from \"./is-module.ts\";\n\nexport function addDefault(\n  path: NodePath,\n  importedSource: string,\n  opts?: Partial<ImportOptions>,\n) {\n  return new ImportInjector(path).addDefault(importedSource, opts);\n}\n\nfunction addNamed(\n  path: NodePath,\n  name: string,\n  importedSource: string,\n  opts?: Omit<\n    Partial<ImportOptions>,\n    \"ensureLiveReference\" | \"ensureNoContext\"\n  >,\n): t.Identifier;\nfunction addNamed(\n  path: NodePath,\n  name: string,\n  importedSource: string,\n  opts?: Omit<Partial<ImportOptions>, \"ensureLiveReference\"> & {\n    ensureLiveReference: true;\n  },\n): t.MemberExpression;\nfunction addNamed(\n  path: NodePath,\n  name: string,\n  importedSource: string,\n  opts?: Omit<Partial<ImportOptions>, \"ensureNoContext\"> & {\n    ensureNoContext: true;\n  },\n): t.SequenceExpression;\n/**\n * add a named import to the program path of given path\n *\n * @export\n * @param {NodePath} path The starting path to find a program path\n * @param {string} name The name of the generated binding. Babel will prefix it with `_`\n * @param {string} importedSource The source of the import\n * @param {Partial<ImportOptions>} [opts]\n * @returns {t.Identifier | t.MemberExpression | t.SequenceExpression} If opts.ensureNoContext is true, returns a SequenceExpression,\n *   else if opts.ensureLiveReference is true, returns a MemberExpression, else returns an Identifier\n */\nfunction addNamed(\n  path: NodePath,\n  name: string,\n  importedSource: string,\n  opts?: Partial<ImportOptions>,\n) {\n  return new ImportInjector(path).addNamed(name, importedSource, opts);\n}\nexport { addNamed };\n\nexport function addNamespace(\n  path: NodePath,\n  importedSource: string,\n  opts?: Partial<ImportOptions>,\n) {\n  return new ImportInjector(path).addNamespace(importedSource, opts);\n}\n\nexport function addSideEffect(\n  path: NodePath,\n  importedSource: string,\n  opts?: Partial<ImportOptions>,\n) {\n  return new ImportInjector(path).addSideEffect(importedSource, opts);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,eAAA,GAAAC,OAAA;AAMA,IAAAC,SAAA,GAAAD,OAAA;AAEO,SAASE,UAAUA,CACxBC,IAAc,EACdC,cAAsB,EACtBC,IAA6B,EAC7B;EACA,OAAO,IAAIC,uBAAc,CAACH,IAAI,CAAC,CAACD,UAAU,CAACE,cAAc,EAAEC,IAAI,CAAC;AAClE;AAsCA,SAASE,QAAQA,CACfJ,IAAc,EACdK,IAAY,EACZJ,cAAsB,EACtBC,IAA6B,EAC7B;EACA,OAAO,IAAIC,uBAAc,CAACH,IAAI,CAAC,CAACI,QAAQ,CAACC,IAAI,EAAEJ,cAAc,EAAEC,IAAI,CAAC;AACtE;AAGO,SAASI,YAAYA,CAC1BN,IAAc,EACdC,cAAsB,EACtBC,IAA6B,EAC7B;EACA,OAAO,IAAIC,uBAAc,CAACH,IAAI,CAAC,CAACM,YAAY,CAACL,cAAc,EAAEC,IAAI,CAAC;AACpE;AAEO,SAASK,aAAaA,CAC3BP,IAAc,EACdC,cAAsB,EACtBC,IAA6B,EAC7B;EACA,OAAO,IAAIC,uBAAc,CAACH,IAAI,CAAC,CAACO,aAAa,CAACN,cAAc,EAAEC,IAAI,CAAC;AACrE", "ignoreList": []}