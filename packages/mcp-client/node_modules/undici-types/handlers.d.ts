import Dispatcher from "./dispatcher";

export declare class Redirect<PERSON><PERSON>ler implements <PERSON><PERSON>atcher.DispatchHandlers {
  constructor(
    dispatch: Dispatcher,
    maxRedirections: number,
    opts: Dispatcher.DispatchOptions,
    handler: Dispatcher.DispatchHandlers,
    redirectionLimitReached: boolean
  );
}

export declare class Decorator<PERSON><PERSON>ler implements Dispatcher.DispatchHandlers {
  constructor(handler: Dispatcher.DispatchHandlers);
}
